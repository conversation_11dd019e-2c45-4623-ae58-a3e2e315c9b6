import React from 'react';
import Image from 'next/image';

const HeroShapes = () => (
  <>
    {/* Shape 1 - Circle with rotation and float ; Bubble (top left, bigger) */}
    <div className='absolute top-[-1rem] left-0 w-[10rem] h-[10rem] '>
      <Image 
        src='/img/circle.svg'
        alt='floating circle'
        width={160}
        height={160}
        className='object-contain scale-200 animate-spin-slow hover:scale-250 transition-transform'
        draggable={false}
      />
    </div>
    {/* Shape 2 - Cube with blur and scale (Cube 1 - top right) */}
    <div className='absolute top-0 right-0 w-[11rem] h-[11rem] animate-float-delayed'>
      <Image 
        src='/img/cube.svg'
        alt='floating cube'
        width={300}
        height={300}
        className='object-contain blur-[1px] scale-105 hover:blur-none transition-all transform rotate-[80deg]'
        draggable={false}
      />
    </div>
     {/* Shape 3 cube two scale and on left (Cube 2 - bottom left) */}
    <div className='absolute bottom-[-2rem] left-[-1rem] w-[11rem] h-[11rem] animate-float-delayed z-0'>
      <Image 
        src='/img/cube.svg'
        alt='floating cube'
        width={300}
        height={300}
        className='object-contain blur-[1px] scale-200 hover:blur-none transition-all transform rotate-[100deg] transform-gpu'
        draggable={false}
      />
    </div>
    {/* Shape 4 - small Cube with blur, bigger size, and anticlockwise spin */}
    <div className='absolute top-[30rem] right-[33rem] w-[5rem] h-[5rem] animate-float-reverse'>
      <Image 
        src='/img/cube.svg'
        alt='floating cube'
        width={120}
        height={120}
        className='object-contain hover:blur-none transition-all transform-gpu animate-spin-slow'
        draggable={false}
      />  
    </div>
    {/* Shape 5 - small Cube with blur, bigger size, and anticlockwise spin */}
    <div className='absolute bottom-[20rem] left-[34rem] w-[5rem] h-[5rem] animate-float-reverse'>
      <Image 
        src='/img/cube.svg'
        alt='floating cube'
        width={120}
        height={120}
        className='object-contain hover:blur-none transition-all transform-gpu animate-spin-reverse'
        draggable={false}
      />
    </div>
   
  </>
);

export default HeroShapes;
