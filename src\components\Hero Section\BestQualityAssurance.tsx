"use client";
import React from 'react';
import { motion } from 'framer-motion';

const BestQualityAssurance = () => {
  return (
    <motion.div 
      className="box-border content-stretch flex flex-col gap-[5px] items-end justify-start leading-[0] p-0 relative shrink-0 text-center text-neutral-900 text-nowrap"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.6, delay: 2.1, type: "spring" }}
    >
      <div className="font-['Urbanist:Bold',_sans-serif] font-bold relative shrink-0 text-[47px] tracking-[-0.705px]">
        <motion.p 
          className="adjustLetterSpacing block leading-none text-nowrap whitespace-pre"
          animate={{ color: ["#000000", "#feb273", "#000000"] }}
          transition={{ duration: 2, repeat: Infinity, delay: 3 }}
        >
          Best Quality
        </motion.p>
      </div>
      <div className="font-['Lufga:Regular',_sans-serif] not-italic relative shrink-0 text-[20px] tracking-[-0.3px]">
        <p className="adjustLetterSpacing block leading-none text-nowrap whitespace-pre">
          Assurance
        </p>
      </div>
    </motion.div>
  );
};

export default BestQualityAssurance;
