import { PortfolioItem } from '@/types/portfolio';

export const portfolioItems: PortfolioItem[] = [
  {
    id: 'slogan-intro',
    type: 'hero-text',
    gridArea: 'slogan-intro',
    className: 'bg-[#fadcd9]',
    content: {
      title: 'Artist Redefining',
      subtitle: 'Architecture',
      subtitleSuffix: 'with AI-Driven Design',
      textColor: 'text-[#000000]',
      fontSize: 'text-[56px]',
      lineHeight: 'leading-[60px]',
      fontFamily: "font-['<PERSON><PERSON>:Bold',_sans-serif]"
    }
  },
  {
    id: 'portrait',
    type: 'image',
    gridArea: 'portrait',
    className: '',
    content: {
      image: '/img/layput_card-2.png',
      title: '<PERSON>',
    }
  },
  {
    id: 'work',
    type: 'work-showcase',
    gridArea: 'work',
    className: 'bg-[#fadcd9]',
    content: {
      title: '<PERSON><PERSON>',
      mainImage: '/img/layput_card-3.png',
      projectList: ['Elara', 'Verve', 'Zephyr'],
      textColor: 'text-[#000000]',
      fontSize: 'text-[25px]',
      fontFamily: "font-['<PERSON><PERSON>:Medium',_sans-serif]",
      socialFrame: {
        title: 'Social Projects',
        subtitle: 'Follow our journey',
        hasArrow: true,
        socialIcons: ['instagram', 'linkedin', 'twitter']
      }
    }
  },
  {
    id: 'about',
    type: 'about',
    gridArea: 'about',
    className: 'bg-[#fadcd9]',
    content: {
      description: 'Julia Huang is an innovative AI artist, renowned for blending cutting-edge technology with creative expression. Based in LA, she crafts unique digital art experiences accessible globally.',
      textColor: 'text-[#000000]',
      fontSize: 'text-[20px]',
      lineHeight: 'leading-[25px]',
      fontFamily: "font-['Gilroy:Light',_sans-serif]",
      hasCircleIcon: true,
    }
  },
  {
    id: 'contact',
    type: 'contact',
    gridArea: 'contact',
    className: 'bg-[#f8afa6]',
    content: {
      title: 'Contact me',
      subtitle: 'Have some questions?',
      hasArrow: true,
      textColor: 'text-[#000000]',
      titleFontSize: 'text-[55px]',
      subtitleFontSize: 'text-[15px]',
      titleFontFamily: "font-['Gilroy:Medium',_sans-serif]",
      subtitleFontFamily: "font-['Gilroy:Light',_sans-serif]"
    }
  },

  {
    id: 'social-links',
    type: 'social',
    gridArea: 'social-links',
    className: 'bg-[#fadcd9]',
    content: {
      socialLinks: [
        { platform: 'Instagram', url: 'https://instagram.com', label: 'INSTAGRAM' },
        { platform: 'Twitter', url: 'https://twitter.com', label: 'TWITTER' },
        { platform: 'LinkedIn', url: 'https://linkedin.com', label: 'LINKEDIN' },
      ],
      textColor: 'text-[#000000]',
      fontSize: 'text-[15px]',
      fontFamily: "font-['Gilroy:Light',_sans-serif]"
    }
  }
];

export const gridConfig = {
  columns: 12,
  rows: 6,
  gap: '16px',
  areas: [
    'slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work',
    'slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work',
    'slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work',
    'about about about about contact contact contact contact work work work work',
    'about about about about contact contact contact contact work work work work',
    'social-links social-links social-links social-links . . . . . . . .'
  ]
};
