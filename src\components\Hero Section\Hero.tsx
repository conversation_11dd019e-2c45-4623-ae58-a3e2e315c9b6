"use client";
import React from 'react'
import Image from 'next/image'

import HeroShapes from './HeroShapes'
import Navbar from '../Navbar';
import PortfolioGrid from './PortfolioGrid';

const Hero = () => {
  return (
    <div className="relative bg-white w-full">
      {/* Large Orange Circle - moved to root level to act as background for both sections */}
      <div className="absolute left-[50%] top-[96vh] -translate-x-1/2 -translate-y-1/2 w-[60rem] h-[60rem] bg-[#F9B87B] rounded-full z-0 opacity-95"></div>

      {/* Main Section */}
      <section className="relative h-[100vh] w-full flex flex-col items-center justify-center pt-32 z-10">

        {/* Hero floating shapes */}
        <HeroShapes />

        {/* Headings */}
        <h1 className="relative z-10 text-[15rem] leading-none font-serif font-extrabold text-[#0F1117] tracking-tight select-none -mt-30" style={{fontFamily: 'Georgia, serif'}}>PerPixel</h1>
        <h2 className="relative z-10 mt-[-1.5rem] text-[12rem] leading-none font-serif font-extrabold text-white tracking-tight select-none" style={{fontFamily: 'Georgia, serif'}}>Agency</h2>

        {/* Buttons */}
        <div className="absolute left-1/2 bottom-16 -translate-x-1/2 z-10 flex gap-6 px-8 py-6 rounded-full bg-white/30 backdrop-blur-md border border-white/30 shadow-lg">
          <button className="bg-[#0F1117] text-white px-10 py-4 text-2xl rounded-full font-semibold flex items-center gap-3 hover:bg-[#1A1E29] transition-colors shadow-md">
            Portfolio <span className="ml-1">↗</span>
          </button>
          <button className="bg-white text-[#0F1117] px-10 py-4 text-2xl rounded-full font-semibold hover:bg-[#F9B87B] hover:border-[#F9B87B] transition-colors shadow-md">
            Hire me
          </button>
        </div>

        {/* Right Quote (moved to right, vertical alignment) */}
        <div className="absolute right-12 top-2/3 w-[320px] z-10 flex flex-col items-start">
          <span className="text-[5rem] text-[#23283B] leading-none -mb-6">“</span>
          <span className="text-black text-3xl font-medium leading-tight">Our team's exceptional product design ensure website’s success.</span>
        </div>

        {/* Left Testimonial, Rating, Experience (moved to left) */}
        <div className="absolute left-4 top-1/3 flex flex-col items-start z-10">
          <div className="flex items-center gap-2 mb-2">
            {[...Array(5)].map((_,i) => (
              <span key={i} className="text-[#F9B87B] text-xl">★</span>
            ))}
          </div>
          <div className="text-[#23283B] text-2xl font-semibold -mt-1">Best Quality</div>
          <div className="text-[#181C2A] text-3xl font-bold">Assurance</div>
        </div>
      </section>
      <div className="relative z-10">
        <PortfolioGrid />
      </div>
    </div>
  )
}

export default Hero