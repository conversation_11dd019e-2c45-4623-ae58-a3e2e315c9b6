@import "tailwindcss";

/* Color Palette from Reference Design */
@theme {
  --color-primary: #030213;
  --color-primary-foreground: #ffffff;
  --color-secondary: #ececf0;
  --color-secondary-foreground: #030213;
  --color-muted: #ececf0;
  --color-muted-foreground: #717182;
  --color-accent: #e9ebef;
  --color-accent-foreground: #030213;
  --color-destructive: #d4183d;
  --color-destructive-foreground: #ffffff;
  --color-border: rgba(0, 0, 0, 0.1);
  --color-input: transparent;
  --color-input-background: #f3f3f5;
  --color-switch-background: #cbced4;
  --color-ring: #b3b3b3;

  /* Custom Brand Colors */
  --color-brand-orange: #feb273;
  --color-brand-pink: #fadcd9;
  --color-brand-coral: #f8afa6;
  --color-brand-dark: #131d26;
  --color-brand-gray: #344054;
  --color-brand-light-gray: #d0d5dd;

  /* Chart Colors */
  --color-chart-1: #ff8c42;
  --color-chart-2: #6bb6ff;
  --color-chart-3: #4c956c;
  --color-chart-4: #f2cc8f;
  --color-chart-5: #e07a5f;
}

/* Custom Keyframes for HeroShapes */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}
@keyframes float-reverse {
  0% { transform: translateY(0px); }
  50% { transform: translateY(20px); }
  100% { transform: translateY(0px); }
}
@keyframes float-delayed {
  0% { transform: translateY(0px); }
  25% { transform: translateY(-10px); }
  50% { transform: translateY(-25px); }
  75% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}
@keyframes spin-slow {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
@keyframes spin-reverse {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(-360deg); }
}
@keyframes bounce-slow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

/* Animation Utilities */
.animate-float {
  animation: float 4s ease-in-out infinite;
}
.animate-float-reverse {
  animation: float-reverse 4s ease-in-out infinite;
}
.animate-float-delayed {
  animation: float-delayed 6s ease-in-out infinite;
}
.animate-spin-slow {
  animation: spin-slow 10s linear infinite;
}
.animate-spin-reverse {
  animation: spin-reverse 10s linear infinite;
}
.animate-bounce-slow {
  animation: bounce-slow 3s ease-in-out infinite;
}


:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html, body {
  overflow-x: hidden;
  scrollbar-width: none; /* Firefox */
}
body::-webkit-scrollbar {
  display: none;
}
body {
  -ms-overflow-style: none; /* IE and Edge */
}
body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom Fonts */
@font-face {
  font-family: 'Gilroy';
  src: url('/fonts/Gilroy-Regular.woff2') format('woff2'),
       url('/fonts/Gilroy-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gilroy';
  src: url('/fonts/Gilroy-Medium.woff2') format('woff2'),
       url('/fonts/Gilroy-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gilroy';
  src: url('/fonts/Gilroy-Bold.woff2') format('woff2'),
       url('/fonts/Gilroy-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gilroy';
  src: url('/fonts/Gilroy-Light.woff2') format('woff2'),
       url('/fonts/Gilroy-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gilroy';
  src: url('/fonts/Gilroy-LightItalic.woff2') format('woff2'),
       url('/fonts/Gilroy-LightItalic.woff') format('woff');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Lufga';
  src: url('/fonts/Lufga-Regular.woff2') format('woff2'),
       url('/fonts/Lufga-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lufga';
  src: url('/fonts/Lufga-Medium.woff2') format('woff2'),
       url('/fonts/Lufga-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lufga';
  src: url('/fonts/Lufga-Light.woff2') format('woff2'),
       url('/fonts/Lufga-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

/* Additional Font Families from Reference */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat-Bold.woff2') format('woff2'),
       url('/fonts/Montserrat-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Glegoo';
  src: url('/fonts/Glegoo-Bold.woff2') format('woff2'),
       url('/fonts/Glegoo-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Urbanist';
  src: url('/fonts/Urbanist-Bold.woff2') format('woff2'),
       url('/fonts/Urbanist-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* Font Family Utilities */
.font-lufga { font-family: 'Lufga', sans-serif; }
.font-gilroy { font-family: 'Gilroy', sans-serif; }
.font-montserrat { font-family: 'Montserrat', sans-serif; }
.font-glegoo { font-family: 'Glegoo', serif; }
.font-urbanist { font-family: 'Urbanist', sans-serif; }

/* Utility Classes */
.adjustLetterSpacing {
  letter-spacing: -0.02em;
}
