"use client";
import React from 'react';
import { motion } from 'framer-motion';

// Portfolio Button Component
const Portfolio = () => {
  return (
    <motion.div
      className="basis-0 grow min-h-px min-w-px relative rounded-[60px] shrink-0 cursor-pointer"
      data-name="Portfolio"
      whileHover={{ backgroundColor: "#131d26" }}
      whileTap={{ scale: 0.95 }}
      onClick={() => {
        // Scroll to portfolio section or handle portfolio navigation
        const portfolioSection = document.getElementById('portfolio');
        if (portfolioSection) {
          portfolioSection.scrollIntoView({ behavior: 'smooth' });
        }
      }}
    >
      <div className="flex flex-row items-center justify-center overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-center px-5 py-2.5 relative w-full">
          <div className="font-['Lufga:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#ffffff] text-[25.692px] text-left text-nowrap tracking-[-0.3854px]">
            <p className="adjustLetterSpacing block leading-[normal] whitespace-pre">
              Portfolio
            </p>
          </div>
          <div className="flex-none">
            <svg
              className="block size-[25.692px]"
              fill="none"
              preserveAspectRatio="none"
              viewBox="0 0 26 26"
            >
              <path
                d="M6.32048 19.6796C5.98201 19.341 5.98201 18.7923 6.32048 18.4539L17.841 6.93332H10.4C9.92132 6.93332 9.5333 6.54529 9.5333 6.06665C9.5333 5.58801 9.92132 5.19998 10.4 5.19998H19.9333C20.1631 5.19998 20.3836 5.2913 20.5462 5.45383C20.7086 5.61637 20.8 5.83679 20.8 6.06665V15.6C20.8 16.0786 20.4119 16.4667 19.9333 16.4667C19.4547 16.4667 19.0666 16.0786 19.0666 15.6V8.15897L7.54612 19.6796C7.20767 20.0179 6.65893 20.0179 6.32048 19.6796Z"
                fill="white"
              />
            </svg>
          </div>
        </div>
      </div>
      <div className="absolute bg-[#131d26] inset-0 pointer-events-none rounded-[60px]" />
    </motion.div>
  );
};

// HireMe Button Component
const HireMe = () => {
  return (
    <motion.div
      className="basis-0 grow min-h-px min-w-px relative rounded-[60px] shrink-0 cursor-pointer"
      data-name="Hire me"
      whileHover={{ backgroundColor: "#feb273" }}
      whileTap={{ scale: 0.95 }}
      onClick={() => {
        const contactSection = document.getElementById('contact');
        if (contactSection) {
          contactSection.scrollIntoView({ behavior: 'smooth' });
        }
      }}
    >
      <div className="flex flex-row items-center justify-center overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-center px-5 py-2.5 relative w-full">
          <div className="font-['Lufga:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#131d26] text-[25.692px] text-left text-nowrap tracking-[-0.3854px]">
            <p className="adjustLetterSpacing block leading-[normal] whitespace-pre">
              Hire me
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Enhanced Button5 Component
const Button5 = () => {
  return (
    <motion.div
      className="absolute backdrop-blur-[7.5px] backdrop-filter bg-[rgba(255,255,255,0.1)] h-[83.841px] rounded-[50px] left-1/2 bottom-16 -translate-x-1/2 w-[348px]"
      data-name="Button"
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, delay: 1.2 }}
      whileHover={{ y: -5 }}
    >
      <div className="box-border content-stretch flex flex-row gap-2.5 h-[83.841px] items-center justify-center overflow-clip p-[10px] relative w-[348px]">
        <Portfolio />
        <HireMe />
      </div>
      <div className="absolute border-2 border-[#ffffff] border-solid inset-0 pointer-events-none rounded-[50px]" />
    </motion.div>
  );
};

export default Button5;
export { Portfolio, HireMe };
