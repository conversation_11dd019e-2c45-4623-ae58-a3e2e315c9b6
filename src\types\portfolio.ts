export interface PortfolioItem {
  id: string;
  type: 'hero-text' | 'image' | 'contact' | 'about' | 'work-showcase' | 'social';
  gridArea: string;
  className?: string;
  content: {
    title?: string;
    subtitle?: string;
    subtitleSuffix?: string;
    description?: string;
    image?: string;
    images?: string[];
    mainImage?: string;
    projectList?: string[];
    link?: string;
    hasArrow?: boolean;
    hasFlowerIcon?: boolean;
    hasCircleIcon?: boolean;
    backgroundColor?: string;
    textColor?: string;
    fontSize?: string;
    lineHeight?: string;
    fontFamily?: string;
    letterSpacing?: string;
    titleFontSize?: string;
    subtitleFontSize?: string;
    titleFontFamily?: string;
    subtitleFontFamily?: string;
    items?: string[];
    socialFrame?: {
      title: string;
      subtitle: string;
      hasArrow: boolean;
      socialIcons?: string[];
    };
    socialLinks?: {
      platform: string;
      url: string;
      label: string;
    }[];
  };
}

export interface GridConfig {
  columns: number;
  rows: number;
  gap: string;
  areas: string[];
}
