"use client";
import React from "react";
import { motion } from "framer-motion";
import PortfolioCard from "../Portfolio/PortfolioCard";
import { portfolioItems, gridConfig } from "@/data/portfolioData";

const PortfolioGrid = () => {
  return (
    <section className="relative w-full px-6 py-16 overflow-hidden">
      {/* Desktop Grid */}
      <motion.div
        className="portfolio-grid w-full max-w-[1400px] mx-auto hidden lg:grid relative"
        style={{
          display: 'grid',
          gridTemplateColumns: `repeat(${gridConfig.columns}, 1fr)`,
          gridTemplateRows: `repeat(${gridConfig.rows}, minmax(140px, 1fr))`,
          gridTemplateAreas: gridConfig.areas.map(area => `"${area}"`).join(' '),
          gap: gridConfig.gap,
          minHeight: '800px',
        }}
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, staggerChildren: 0.1 }}
        viewport={{ once: true }}
      >
        {portfolioItems.map((item, index) => (
          <motion.div
            key={item.id}
            style={{ gridArea: item.gridArea }}
            className="min-h-[120px]"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{
              duration: 0.6,
              delay: index * 0.1,
              ease: "easeOut"
            }}
            viewport={{ once: true }}
          >
            <PortfolioCard item={item} />
          </motion.div>
        ))}
      </motion.div>

      {/* Tablet Grid */}
      <motion.div
        className="hidden md:grid lg:hidden w-full max-w-5xl mx-auto grid-cols-8 grid-rows-10 gap-4 min-h-[1000px]"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        {portfolioItems.map((item, index) => {
          const tabletLayouts = [
            'col-span-5 row-span-3', // hero-text
            'col-span-3 row-span-3', // profile
            'col-span-4 row-span-4', // work-showcase
            'col-span-4 row-span-3', // about-card
            'col-span-4 row-span-3', // contact-card
            'col-span-8 row-span-1', // social-links
          ];

          return (
            <motion.div
              key={item.id}
              className={`${tabletLayouts[index]} min-h-[120px]`}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <PortfolioCard item={item} />
            </motion.div>
          );
        })}
      </motion.div>

      {/* Mobile Grid */}
      <motion.div
        className="grid md:hidden w-full gap-6 grid-cols-1 max-w-md mx-auto"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        {portfolioItems.map((item, index) => (
          <motion.div
            key={item.id}
            className="min-h-[250px]"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.15 }}
            viewport={{ once: true }}
          >
            <PortfolioCard item={item} />
          </motion.div>
        ))}
      </motion.div>
    </section>
  );
};

export default PortfolioGrid;
